use cpal::traits::{<PERSON><PERSON><PERSON><PERSON><PERSON>, HostTrait, StreamTrait};
use cpal::{<PERSON><PERSON>, SampleFormat, Stream, StreamConfig};
use once_cell::sync::Lazy;
use ringbuf::{<PERSON><PERSON><PERSON>onsumer, HeapProducer, HeapRb};
use rtlsdr::{get_device_count, get_device_name, open};
use rustfft::{num_complex::Complex32, FftPlanner};
use serde::Serialize;
use std::collections::VecDeque;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;
use tauri::{Emitter, State};

// Store device indices that have been opened
static OPEN_DEVICES: Lazy<Mutex<Vec<i32>>> = Lazy::new(|| Mutex::new(Vec::new()));

// Global device access control
static DEVICE_IN_USE: Lazy<Mutex<Option<i32>>> = Lazy::new(|| Mutex::new(None));

// Audio demodulation modes
#[derive(<PERSON><PERSON>, Copy, Debug, Serialize)]
enum DemodulationMode {
    FM,
    AM,
    USB, // Upper Side Band
    LSB, // Lower Side Band
}

// Audio state management
#[derive(Default)]
struct AudioState {
    is_streaming: bool,
    volume: f32,
    muted: bool,
    demod_mode: Option<DemodulationMode>,
    sample_rate: u32,
    squelch_threshold: f32,
}

impl AudioState {
    fn new() -> Self {
        Self {
            is_streaming: false,
            volume: 0.8, // 80% volume for better FM radio audibility
            muted: false,
            demod_mode: Some(DemodulationMode::FM),
            sample_rate: 48000,      // Standard audio sample rate
            squelch_threshold: 0.01, // Default squelch threshold
        }
    }
}

type AudioStateRef = Arc<Mutex<AudioState>>;

// Audio streaming state
struct AudioStreamState {
    is_active: AtomicBool,
    audio_producer: Option<HeapProducer<f32>>,
}

impl AudioStreamState {
    fn new() -> Self {
        Self {
            is_active: AtomicBool::new(false),
            audio_producer: None,
        }
    }
}

// We'll manage the stream differently to avoid thread safety issues

type AudioStreamStateRef = Arc<Mutex<AudioStreamState>>;

// Demodulation functions
fn demodulate_fm(iq: &[Complex32], sample_rate: f32, squelch_threshold: f32) -> Vec<f32> {
    let mut out = Vec::with_capacity(iq.len());
    if iq.is_empty() {
        return out;
    }

    let two_pi = std::f32::consts::PI * 2.0;
    let mut prev = iq[0].im.atan2(iq[0].re);
    out.push(0.0);

    // Calculate signal strength for squelch
    let signal_strength: f32 = iq.iter().map(|c| c.norm()).sum::<f32>() / iq.len() as f32;

    // Squelch - mute if signal is too weak
    let is_squelched = signal_strength < squelch_threshold;

    // Debug: Log squelch activity occasionally
    static mut SQUELCH_LOG_COUNTER: u32 = 0;
    unsafe {
        SQUELCH_LOG_COUNTER += 1;
        if SQUELCH_LOG_COUNTER % 1000 == 0 {
            println!(
                "FM Demod: Signal strength {:.4}, threshold {:.4}, squelched: {}",
                signal_strength, squelch_threshold, is_squelched
            );
        }
    }

    for &c in &iq[1..] {
        let phase = c.im.atan2(c.re);
        let mut d = phase - prev;

        // Phase unwrapping
        if d > std::f32::consts::PI {
            d -= two_pi;
        } else if d < -std::f32::consts::PI {
            d += two_pi;
        }

        // Convert phase difference to audio sample
        // Scale down the frequency deviation for proper audio levels
        let audio_sample = if is_squelched {
            0.0 // Mute weak signals
        } else {
            // Scale the frequency deviation to audio range
            // FM deviation is typically ±75kHz, scale to ±1.0 audio range
            (d / two_pi) * sample_rate / 75000.0
        };

        out.push(audio_sample);
        prev = phase;
    }
    out
}

#[allow(dead_code)]
fn demodulate_am(iq_samples: &[Complex32]) -> Vec<f32> {
    // First pass: calculate all magnitudes
    let magnitudes: Vec<f32> = iq_samples
        .iter()
        .map(|sample| (sample.re * sample.re + sample.im * sample.im).sqrt())
        .collect();

    // Calculate DC component (average)
    let dc_component = magnitudes.iter().sum::<f32>() / magnitudes.len() as f32;

    // Second pass: remove DC and scale
    magnitudes
        .iter()
        .map(|&magnitude| {
            let audio_sample = (magnitude - dc_component) * 5.0; // Increased gain
            audio_sample.max(-1.0).min(1.0) // Clamp to prevent distortion
        })
        .collect()
}

#[allow(dead_code)]
fn demodulate_ssb(iq_samples: &[Complex32], upper_sideband: bool) -> Vec<f32> {
    // Simple SSB demodulation using real part for USB, imaginary for LSB
    iq_samples
        .iter()
        .map(|sample| {
            if upper_sideband {
                sample.re // USB: use real part
            } else {
                sample.im // LSB: use imaginary part
            }
        })
        .collect()
}

// Apply simple low-pass filter for audio
#[allow(dead_code)]
fn apply_audio_filter(samples: &mut [f32], cutoff_ratio: f32) {
    let alpha = cutoff_ratio;
    let mut prev = 0.0f32;

    for sample in samples.iter_mut() {
        *sample = alpha * *sample + (1.0 - alpha) * prev;
        prev = *sample;
    }
}

fn apply_antialias_filter(samples: &mut [f32], cutoff_ratio: f32) {
    // single-pole low-pass, cutoff_ratio = fc/(fs/2)
    let alpha = cutoff_ratio;
    let mut prev = samples[0];
    for x in samples.iter_mut().skip(1) {
        *x = alpha * *x + (1.0 - alpha) * prev;
        prev = *x;
    }
}

// Better resampling function with linear interpolation
#[allow(dead_code)]
fn resample_audio(input: &[f32], input_rate: f32, output_rate: f32) -> Vec<f32> {
    if input.is_empty() {
        return Vec::new();
    }

    let ratio = input_rate / output_rate;
    let output_len = (input.len() as f32 / ratio) as usize;
    let mut output = Vec::with_capacity(output_len);

    for i in 0..output_len {
        let src_index = i as f32 * ratio;
        let src_index_floor = src_index.floor() as usize;
        let src_index_ceil = (src_index_floor + 1).min(input.len() - 1);
        let frac = src_index - src_index_floor as f32;

        if src_index_floor < input.len() {
            // Linear interpolation between samples
            let sample = input[src_index_floor] * (1.0 - frac) + input[src_index_ceil] * frac;
            output.push(sample);
        }
    }

    output
}

// 60Hz notch filter to remove power line hum
fn apply_60hz_notch_filter(samples: &mut [f32], sample_rate: f32) {
    if samples.len() < 3 {
        return;
    }

    // Design a notch filter for 60Hz and its harmonics (120Hz, 180Hz)
    let notch_frequencies = [60.0, 120.0, 180.0];

    for &notch_freq in &notch_frequencies {
        // Calculate filter coefficients for notch filter
        let omega = 2.0 * std::f32::consts::PI * notch_freq / sample_rate;
        let q = 10.0; // Quality factor - higher Q = narrower notch
        let alpha = omega.sin() / (2.0 * q);

        // Notch filter coefficients (biquad)
        let b0 = 1.0;
        let b1 = -2.0 * omega.cos();
        let b2 = 1.0;
        let a0 = 1.0 + alpha;
        let a1 = -2.0 * omega.cos();
        let a2 = 1.0 - alpha;

        // Normalize coefficients
        let b0 = b0 / a0;
        let b1 = b1 / a0;
        let b2 = b2 / a0;
        let a1 = a1 / a0;
        let a2 = a2 / a0;

        // Apply biquad filter
        let mut x1 = 0.0;
        let mut x2 = 0.0;
        let mut y1 = 0.0;
        let mut y2 = 0.0;

        for sample in samples.iter_mut() {
            let x0 = *sample;
            let y0 = b0 * x0 + b1 * x1 + b2 * x2 - a1 * y1 - a2 * y2;

            *sample = y0;

            // Update delay line
            x2 = x1;
            x1 = x0;
            y2 = y1;
            y1 = y0;
        }
    }
}

// Apply a more aggressive low-pass filter for FM audio
// 🎵 VOICE/MUSIC DETECTOR - Distinguishes real audio from noise
#[allow(dead_code)]
fn analyze_audio_content(samples: &[f32]) -> (f32, bool, String) {
    if samples.len() < 100 {
        return (0.0, false, "Too few samples".to_string());
    }

    // Calculate RMS level (signal strength)
    let rms = (samples.iter().map(|&x| x * x).sum::<f32>() / samples.len() as f32).sqrt();

    // Calculate dynamic range (voice/music varies, noise is constant)
    let max_sample = samples.iter().fold(0.0f32, |a, &b| a.max(b.abs()));
    let min_sample = samples.iter().fold(f32::INFINITY, |a, &b| a.min(b.abs()));
    let dynamic_range = if min_sample > 0.0 {
        max_sample / min_sample
    } else {
        1.0
    };

    // Calculate zero-crossing rate (voice has more zero crossings than pure noise)
    let mut zero_crossings = 0;
    for i in 1..samples.len() {
        if (samples[i] >= 0.0) != (samples[i - 1] >= 0.0) {
            zero_crossings += 1;
        }
    }
    let zcr = zero_crossings as f32 / samples.len() as f32;

    // Calculate spectral centroid (voice/music has energy in specific bands)
    let mut spectral_energy_low = 0.0f32; // 0-1kHz (voice fundamentals)
    let mut spectral_energy_mid = 0.0f32; // 1-4kHz (voice formants)
    let mut spectral_energy_high = 0.0f32; // 4-8kHz (consonants, music)

    // Simple frequency analysis using time-domain approximation
    for i in 1..samples.len().min(1000) {
        let freq_estimate = (samples[i] - samples[i - 1]).abs();
        if i < 100 {
            spectral_energy_low += freq_estimate;
        } else if i < 400 {
            spectral_energy_mid += freq_estimate;
        } else {
            spectral_energy_high += freq_estimate;
        }
    }

    // Voice/music detection criteria
    let rms_db = 20.0 * rms.log10();
    let has_good_snr = rms_db > -40.0; // At least -40dB signal level
    let has_dynamics = dynamic_range > 2.0; // Voice/music varies by at least 2:1
    let has_voice_spectrum = spectral_energy_mid > spectral_energy_low * 0.5; // Voice formants
    let reasonable_zcr = zcr > 0.01 && zcr < 0.5; // Not too flat, not too noisy

    let is_voice_music = has_good_snr && has_dynamics && (has_voice_spectrum || reasonable_zcr);

    let analysis = format!(
        "RMS: {:.1}dB, Dyn: {:.1}:1, ZCR: {:.3}, Spectrum: L{:.3}/M{:.3}/H{:.3}",
        rms_db, dynamic_range, zcr, spectral_energy_low, spectral_energy_mid, spectral_energy_high
    );

    (rms_db, is_voice_music, analysis)
}

#[allow(dead_code)]
fn apply_basic_fm_filter(samples: &mut Vec<f32>) {
    // Simple low-pass filter for FM audio (15 kHz cutoff)
    if samples.len() < 2 {
        return;
    }

    // Gentler moving average filter (reduced from 0.1 to 0.2)
    let alpha = 0.2; // Low-pass filter coefficient
    for i in 1..samples.len() {
        samples[i] = samples[i] * alpha + samples[i - 1] * (1.0 - alpha);
    }
}

fn apply_fm_audio_filter(samples: &mut [f32]) {
    // 🎵 ENHANCED FM AUDIO FILTER with improved static reduction
    // Designed to preserve audio quality while significantly reducing static

    if samples.is_empty() {
        return;
    }

    // Debug: Log that filtering is being applied
    if samples.len() > 1000 {
        println!("Audio filter: Processing {} samples", samples.len());
    }

    // Stage 1: De-emphasis filter (CRITICAL - matches rtl_fm -E deemp)
    // This is the key difference - FM broadcasts use pre-emphasis, we need de-emphasis
    // NOTE: This should be applied AFTER decimation at the final audio sample rate
    let sample_rate = 48000.0; // Audio sample rate, not SDR rate
    let tau = 75e-6; // 75 microseconds - North American FM de-emphasis time constant
    let alpha = 1.0 / (1.0 + sample_rate * tau);

    let mut prev_deemph = samples[0];
    for sample in samples.iter_mut() {
        let deemphasized = alpha * *sample + (1.0 - alpha) * prev_deemph;
        prev_deemph = deemphasized;
        *sample = deemphasized;
    }

    // Stage 2: 60Hz notch filter to remove power line hum
    apply_60hz_notch_filter(samples, sample_rate);

    // Stage 2b: Noise gate - mute very quiet samples that are likely noise
    let rms = (samples.iter().map(|&x| x * x).sum::<f32>() / samples.len() as f32).sqrt();
    let noise_gate_threshold = rms * 0.1; // Gate samples below 10% of RMS

    for sample in samples.iter_mut() {
        if sample.abs() < noise_gate_threshold {
            *sample *= 0.1; // Reduce noise by 90% instead of complete muting
        }
    }

    // Stage 3: Improved low-pass filter with better coefficients
    if samples.len() >= 7 {
        // Better FIR coefficients for 15kHz cutoff (Hamming window design)
        let fir_coeffs = [0.05, 0.1, 0.2, 0.3, 0.2, 0.1, 0.05]; // 7-tap filter
        let mut filtered = Vec::with_capacity(samples.len());

        // Apply FIR filter
        for i in 0..samples.len() {
            let mut sum = 0.0f32;
            for j in 0..fir_coeffs.len() {
                if i >= j {
                    sum += samples[i - j] * fir_coeffs[j];
                }
            }
            filtered.push(sum);
        }

        // Copy filtered samples back
        samples.copy_from_slice(&filtered);
    }

    // Stage 4: Enhanced DC blocking and hum removal
    if samples.len() >= 2 {
        // First: Remove DC offset (average value)
        let dc_offset = samples.iter().sum::<f32>() / samples.len() as f32;
        for sample in samples.iter_mut() {
            *sample -= dc_offset;
        }

        // Second: High-pass filter to remove low-frequency hum (50/60Hz and harmonics)
        // Use a stronger high-pass filter with cutoff around 80Hz
        let alpha = 0.98; // Higher value = stronger high-pass filtering
        let mut prev_input = samples[0];
        let mut prev_output = 0.0;

        for sample in samples.iter_mut() {
            let current_input = *sample;
            let output = alpha * (prev_output + current_input - prev_input);
            *sample = output;
            prev_input = current_input;
            prev_output = output;
        }
    }
}

// NEW: automatic‐gain‐control helper with improved FM handling
fn agc_normalize(chunk: &mut [f32], target_rms: f32) {
    if chunk.is_empty() {
        return;
    }

    let rms = (chunk.iter().map(|&s| s * s).sum::<f32>() / chunk.len() as f32).sqrt();

    // Only apply AGC if signal is above noise floor
    let noise_floor = 0.001; // Minimum signal level to consider
    if rms > noise_floor {
        // Limit gain to prevent over-amplification of noise
        let max_gain = 10.0; // Maximum 20dB gain
        let gain = (target_rms / rms).min(max_gain);

        // Apply gain with soft limiting to prevent harsh clipping
        for x in chunk.iter_mut() {
            let amplified = *x * gain;
            // Soft clipping using tanh function for smoother distortion
            *x = if amplified.abs() > 0.8 {
                amplified.signum() * (amplified.abs().tanh() * 0.9)
            } else {
                amplified.clamp(-1.0, 1.0)
            };
        }
    } else {
        // Signal is too weak, apply heavy attenuation to reduce noise
        for x in chunk.iter_mut() {
            *x *= 0.1;
        }
    }
}

// Helper function to safely open device with conflict checking
fn safe_open_device(device_index: i32) -> Result<rtlsdr::RTLSDRDevice, String> {
    // Check if device is already in use
    {
        let device_in_use = DEVICE_IN_USE.lock().unwrap();
        if let Some(current_device) = *device_in_use {
            if current_device == device_index {
                return Err(format!("Device {} is already in use", device_index));
            }
        }
    }

    // Try to open the device
    match open(device_index) {
        Ok(device) => {
            // Mark device as in use
            {
                let mut device_in_use = DEVICE_IN_USE.lock().unwrap();
                *device_in_use = Some(device_index);
            }
            Ok(device)
        }
        Err(e) => Err(format!("Failed to open device {}: {:?}", device_index, e)),
    }
}

// Helper function to release device
fn release_device() {
    let mut device_in_use = DEVICE_IN_USE.lock().unwrap();
    *device_in_use = None;
}

// Store signal strength data
#[derive(Default)]
struct SignalData {
    is_monitoring: bool,
    values: Vec<f32>,
    device_index: Option<i32>,
}

impl SignalData {
    fn new() -> Self {
        Self {
            is_monitoring: false,
            values: Vec::new(),
            device_index: None,
        }
    }
}

#[derive(Clone, Serialize)]
#[allow(dead_code)]
struct SignalUpdate {
    value: f32,
    timestamp: u64,
}

type SignalDataState = Arc<Mutex<SignalData>>;

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
fn list_sdr_devices() -> Vec<String> {
    let count = get_device_count();
    let mut devices = Vec::new();

    for i in 0..count {
        let name = get_device_name(i);
        devices.push(format!("{}: {}", i, name));
    }

    devices
}

#[tauri::command]
fn tune_sdr(device_index: usize, frequency: u32) -> Result<String, String> {
    // Convert usize to i32 for rtlsdr library
    let device_index_i32 =
        i32::try_from(device_index).map_err(|_| "Invalid device index".to_string())?;

    // Open the device
    let mut device =
        open(device_index_i32).map_err(|e| format!("Failed to open device: {:?}", e))?;

    // Configure it exactly once:
    device
        .set_sample_rate(2_048_000)
        .map_err(|e| format!("Failed to set sample rate: {:?}", e))?;
    device
        .set_center_freq(frequency)
        .map_err(|e| format!("Failed to set frequency: {:?}", e))?;
    device
        .reset_buffer()
        .map_err(|e| format!("Failed to reset buffer: {:?}", e))?;

    // Track that we've opened this device
    let mut open_devices = OPEN_DEVICES.lock().unwrap();
    if !open_devices.contains(&device_index_i32) {
        open_devices.push(device_index_i32);
    }

    Ok(format!(
        "Tuned device {} to {:.6} MHz",
        device_index,
        frequency as f64 / 1_000_000.0
    ))
}

#[tauri::command]
fn start_signal_monitor(
    device_index: usize,
    window: tauri::Window,
    signal_data: State<'_, SignalDataState>,
    audio_state: State<'_, AudioStateRef>,              // ← NEW
    audio_stream_state: State<'_, AudioStreamStateRef>, // ← NEW
) -> Result<String, String> {
    // Convert usize → i32
    let device_index_i32 =
        i32::try_from(device_index).map_err(|_| "Invalid device index".to_string())?;

    // Update signal_data
    {
        let mut data = signal_data.lock().unwrap();
        data.is_monitoring = true;
        data.device_index = Some(device_index_i32);
        data.values.clear();
    }

    // Clone Arcs for thread
    let _window_clone = window.clone();
    let _signal_data_clone = Arc::clone(&*signal_data);
    let audio_state_clone = Arc::clone(&*audio_state);
    let audio_stream_state_clone = Arc::clone(&*audio_stream_state);

    std::thread::spawn(move || {
        // 1) open device
        let mut device = match safe_open_device(device_index_i32) {
            Ok(dev) => dev,
            Err(e) => {
                eprintln!("Failed to open SDR for signal monitor: {}", e);
                return;
            }
        };

        // — configure the hardware —
        if let Err(e) = device.set_sample_rate(2_048_000) {
            eprintln!("Failed to set sample rate: {:?}", e);
            return;
        }
        if let Err(e) = device.reset_buffer() {
            eprintln!("Failed to reset buffer: {:?}", e);
            return;
        }

        // 2) configure like rtl_fm

        // compute downsample ratio
        const SDR_RATE: usize = 2_048_000;
        let audio_rate = {
            let st = audio_state_clone.lock().unwrap();
            st.sample_rate as usize
        };
        let _ratio = SDR_RATE / audio_rate.max(1); // No longer needed with proper resampling

        // 3) loop & push
        let buf_size = 1024 * 2;
        while audio_stream_state_clone
            .lock()
            .unwrap()
            .is_active
            .load(Ordering::SeqCst)
        {
            match device.read_sync(buf_size) {
                Ok(buf) if buf.len() >= buf_size => {
                    // I/Q → Complex32
                    let mut iq = Vec::with_capacity(buf_size / 2);
                    for chunk in buf.chunks_exact(2) {
                        let i = chunk[0] as i8 as f32 / 128.0;
                        let q = chunk[1] as i8 as f32 / 128.0;
                        iq.push(Complex32::new(i, q));
                    }

                    // FM demod → audio samples (at SDR rate)
                    let squelch_threshold = audio_state_clone.lock().unwrap().squelch_threshold;
                    let mut audio = demodulate_fm(&iq, SDR_RATE as f32, squelch_threshold);

                    // Apply anti-aliasing filter BEFORE resampling
                    apply_antialias_filter(&mut audio, 15000.0 / (SDR_RATE as f32 / 2.0));

                    // Fixed-size decimation for consistent timing
                    let volume = audio_state_clone.lock().unwrap().volume;
                    let decimation_factor = SDR_RATE / audio_rate;

                    // Calculate exact number of output samples we should produce
                    let expected_output_samples = audio.len() / decimation_factor;
                    let mut chunk = Vec::with_capacity(expected_output_samples);

                    // Use precise decimation to maintain consistent sample rate
                    for i in 0..expected_output_samples {
                        let src_idx = i * decimation_factor;
                        if src_idx < audio.len() {
                            chunk.push(audio[src_idx] * volume);
                        }
                    }

                    // Apply de-emphasis and filtering AFTER decimation (at correct sample rate)
                    apply_fm_audio_filter(&mut chunk);

                    // 2) AGC-normalize to target RMS (e.g. 0.1)
                    agc_normalize(&mut chunk, 0.1);

                    // 3) clamp & push into the ring buffer
                    let mut pushed = 0;
                    if let Ok(mut ss) = audio_stream_state_clone.lock() {
                        if let Some(prod) = &mut ss.audio_producer {
                            for &s in &chunk {
                                let s = s.clamp(-1.0, 1.0);
                                if prod.push(s).is_ok() {
                                    pushed += 1;
                                }
                            }
                        }
                    }
                    // Debug: Log buffer status occasionally
                    static mut BUFFER_LOG_COUNTER: u32 = 0;
                    unsafe {
                        BUFFER_LOG_COUNTER += 1;
                        if BUFFER_LOG_COUNTER % 50 == 0 {
                            println!(
                                "Audio: {} raw samples -> {} decimated samples, {} pushed to buffer",
                                audio.len(),
                                chunk.len(),
                                pushed
                            );
                        }
                    }
                }
                Ok(_) => {}
                Err(e) => {
                    eprintln!("Signal monitor read error: {:?}", e);
                    std::thread::sleep(Duration::from_millis(50));
                }
            }
        }

        println!("Signal monitoring stopped");
        release_device();
    });

    Ok("Signal monitoring started".into())
}

#[tauri::command]
fn stop_signal_monitor(signal_data: State<'_, SignalDataState>) -> Result<String, String> {
    let mut data = signal_data.lock().unwrap();
    data.is_monitoring = false;
    data.device_index = None;

    Ok("Signal monitoring stopped".to_string())
}

#[tauri::command]
fn get_signal_data(signal_data: State<'_, SignalDataState>) -> Vec<f32> {
    let data = signal_data.lock().unwrap();
    data.values.clone()
}

// Add a new struct for spectrum data
#[derive(Clone, Serialize)]
struct SpectrumData {
    frequencies: Vec<f32>,
    power_levels: Vec<f32>,
    center_frequency: u32,
}

// Add the get_spectrum command
#[tauri::command]
fn get_spectrum(device_index: usize, center_frequency: u32) -> Result<SpectrumData, String> {
    println!(
        "Getting spectrum for device {} at {} Hz",
        device_index, center_frequency
    );

    // Only use real data, no fallback to dummy data
    get_real_spectrum(device_index, center_frequency)
}

// Function to get real spectrum data
fn get_real_spectrum(device_index: usize, center_frequency: u32) -> Result<SpectrumData, String> {
    // Convert usize to i32 for rtlsdr library
    let device_index_i32 = match i32::try_from(device_index) {
        Ok(idx) => idx,
        Err(_) => return Err("Invalid device index".to_string()),
    };

    // Open the device
    let mut device = match open(device_index_i32) {
        Ok(dev) => dev,
        Err(e) => return Err(format!("Failed to open device: {:?}", e)),
    };

    // — tune the hardware just once —
    device
        .set_sample_rate(2_048_000)
        .map_err(|e| format!("Failed to set sample rate: {:?}", e))?;
    device
        .set_center_freq(center_frequency)
        .map_err(|e| format!("Failed to tune frequency: {:?}", e))?;
    device
        .reset_buffer()
        .map_err(|e| format!("Failed to reset buffer: {:?}", e))?;

    // Set sample rate to 3 MHz for wider bandwidth
    let sample_rate = 3_000_000;
    if let Err(e) = device.set_sample_rate(sample_rate) {
        return Err(format!("Failed to set sample rate: {:?}", e));
    }

    // Set center frequency
    if let Err(e) = device.set_center_freq(center_frequency) {
        return Err(format!("Failed to set frequency: {:?}", e));
    }

    // Increase gain significantly for better sensitivity
    if let Err(e) = device.set_tuner_gain_mode(true) {
        return Err(format!("Failed to set gain mode: {:?}", e));
    }

    // Try to set a higher gain value (40 dB)
    if let Err(e) = device.set_tuner_gain(400) {
        println!("Warning: Failed to set gain: {:?}", e);
        // Continue anyway
    }

    // Reset buffer
    if let Err(e) = device.reset_buffer() {
        return Err(format!("Failed to reset buffer: {:?}", e));
    }

    // Add a longer delay to allow the device to settle
    std::thread::sleep(std::time::Duration::from_millis(300));

    // Buffer size for FFT (must be a power of 2)
    let fft_size = 1024;
    let buffer_size = fft_size * 2; // For I/Q pairs

    // Read samples with retry logic
    let mut buffer = Vec::new();
    let max_retries = 3;

    for attempt in 0..max_retries {
        match device.read_sync(buffer_size) {
            Ok(buf) => {
                buffer = buf;
                break;
            }
            Err(e) => {
                if attempt == max_retries - 1 {
                    return Err(format!(
                        "Failed to read samples after {} attempts: {:?}",
                        max_retries, e
                    ));
                }
                // Reset buffer and wait before retry
                let _ = device.reset_buffer();
                std::thread::sleep(std::time::Duration::from_millis(100));
            }
        }
    }

    if buffer.is_empty() {
        return Err("Received empty buffer from device".to_string());
    }

    // Convert buffer to complex samples for FFT
    let mut samples = Vec::with_capacity(fft_size);
    for i in (0..buffer.len()).step_by(2) {
        if i + 1 < buffer.len() {
            let i_sample = (buffer[i] as f32 - 127.5) / 127.5;
            let q_sample = (buffer[i + 1] as f32 - 127.5) / 127.5;
            samples.push(Complex32::new(i_sample, q_sample));
        }
    }

    // Pad with zeros if we don't have enough samples
    while samples.len() < fft_size {
        samples.push(Complex32::new(0.0, 0.0));
    }

    // Perform FFT
    let mut planner = FftPlanner::new();
    let fft = planner.plan_fft_forward(fft_size);
    let mut fft_samples = samples.clone();

    // Apply Hann window to reduce spectral leakage
    for i in 0..fft_samples.len() {
        let window =
            0.5 * (1.0 - (2.0 * std::f32::consts::PI * i as f32 / fft_samples.len() as f32).cos());
        fft_samples[i] = fft_samples[i] * window;
    }

    fft.process(&mut fft_samples);

    // Calculate power spectrum (and shift FFT so DC is in the center)
    let half_fft: usize = fft_size / 2;

    // First pass: calculate raw power levels and find min/max
    let mut raw_powers: Vec<f32> = Vec::with_capacity(1024);
    let mut max_power = 0.0f32;
    let mut min_power = f32::MAX;

    for i in 0..1024 {
        let fft_index = i % fft_size;
        let idx = (fft_index + half_fft) % fft_size;

        let power = fft_samples[idx].norm();
        raw_powers.push(power);

        if power > max_power {
            max_power = power;
        }
        if power < min_power {
            min_power = power;
        }
    }

    // Calculate noise floor (use 25th percentile as estimate)
    let mut sorted_powers = raw_powers.clone();
    sorted_powers.sort_by(|a, b| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal));
    let noise_floor = sorted_powers[sorted_powers.len() / 4];

    // Second pass: apply non-linear scaling to enhance peaks
    let mut power_levels: Vec<f32> = Vec::with_capacity(1024);
    for power in &raw_powers {
        // Normalize power relative to noise floor
        let normalized = (power - noise_floor) / (max_power - noise_floor);
        let normalized = normalized.max(0.0).min(1.0);

        // Apply non-linear scaling to enhance peaks
        let enhanced = normalized.powf(0.5); // Square root for more emphasis

        // Convert to dB scale with enhancement factor applied
        let db = 10.0 * (power.max(1e-10)).log10();

        // Use the enhanced value to boost stronger signals
        let boosted_db = db + (enhanced * 15.0) - 7.5; // Apply boost based on enhanced value

        power_levels.push(boosted_db.max(-80.0).min(10.0));
    }

    // Apply a wider moving average for smoother display
    let mut smoothed_power_levels: Vec<f32> = Vec::with_capacity(1024);
    for i in 0..1024 {
        let window_size = 11;
        let half_window = window_size / 2;
        let mut sum = 0.0;
        let mut count = 0;

        // Convert i to usize and use explicit type for half_window
        let i_usize = i as usize;
        let half_window_usize = half_window as usize;

        // Use checked arithmetic with explicit types
        let start_idx = if i_usize >= half_window_usize {
            i_usize - half_window_usize
        } else {
            0
        };

        let end_idx = std::cmp::min(i_usize + half_window_usize, 1023);

        for j in start_idx..=end_idx {
            sum += power_levels[j];
            count += 1;
        }

        smoothed_power_levels.push(sum / count as f32);
    }

    // Update the frequencies vector with explicit type
    // Calculate frequency for each bin
    let bin_width = sample_rate as f32 / 1024 as f32;
    let frequencies: Vec<f32> = (0..1024)
        .map(|i| {
            // Calculate frequency offset from center
            let offset = (i as i32 - 512) as f32 * bin_width;
            center_frequency as f32 + offset
        })
        .collect();

    Ok(SpectrumData {
        frequencies,
        power_levels: smoothed_power_levels,
        center_frequency,
    })
}

#[derive(Clone, Serialize)]
struct SpectrumUpdate {
    frequencies: Vec<f32>,
    power_levels: Vec<f32>,
    center_frequency: u32,
    timestamp: u64,
}

// Store streaming state
// Commands for immediate device updates
#[derive(Debug)]
enum DeviceCommand {
    SetFrequency(u32),
    SetPPM(i32),
}

struct StreamingState {
    is_streaming: AtomicBool,
    device_index: Mutex<Option<i32>>,
    center_frequency: Mutex<Option<u32>>,
    ppm_correction: Mutex<Option<i32>>,
    // Add a buffer for time-based averaging
    previous_spectra: Mutex<VecDeque<Vec<f32>>>,
    // Add smoothing window size
    smoothing_window: Mutex<usize>,
    // Add flags to signal immediate updates needed
    frequency_update_needed: AtomicBool,
    ppm_update_needed: AtomicBool,
    // Channel for immediate device commands
    device_command_sender: Mutex<Option<std::sync::mpsc::Sender<DeviceCommand>>>,
}

impl StreamingState {
    fn new() -> Self {
        Self {
            is_streaming: AtomicBool::new(false),
            device_index: Mutex::new(None),
            center_frequency: Mutex::new(None),
            ppm_correction: Mutex::new(None),
            previous_spectra: Mutex::new(VecDeque::with_capacity(5)),
            // Default to 11
            smoothing_window: Mutex::new(11),
            frequency_update_needed: AtomicBool::new(false),
            ppm_update_needed: AtomicBool::new(false),
            device_command_sender: Mutex::new(None),
        }
    }
}

type StreamingStateRef = Arc<StreamingState>;

#[tauri::command]
fn start_spectrum_stream(
    device_index: usize,
    center_frequency: u32,
    window: tauri::Window,
    streaming_state: State<'_, StreamingStateRef>,
) -> Result<String, String> {
    // Convert usize to i32
    let device_index_i32 = match i32::try_from(device_index) {
        Ok(idx) => idx,
        Err(_) => return Err("Invalid device index".to_string()),
    };

    // Check if already streaming
    if streaming_state.is_streaming.load(Ordering::SeqCst) {
        return Err("Already streaming spectrum data".to_string());
    }

    // Update state with mutex
    streaming_state.is_streaming.store(true, Ordering::SeqCst);

    // Use mutex to update the device_index
    if let Ok(mut device_idx) = streaming_state.device_index.lock() {
        *device_idx = Some(device_index_i32);
    } else {
        return Err("Failed to lock device index mutex".to_string());
    }

    // Use mutex to update the center_frequency
    if let Ok(mut freq) = streaming_state.center_frequency.lock() {
        *freq = Some(center_frequency);
    } else {
        return Err("Failed to lock center frequency mutex".to_string());
    }

    // Clone what we need for the thread
    let window_clone = window.clone();
    let streaming_state_clone = Arc::clone(&*streaming_state);

    // Start background thread
    thread::spawn(move || {
        // Get the device index from mutex
        let device_index_i32 = match streaming_state_clone.device_index.lock() {
            Ok(guard) => match *guard {
                Some(idx) => idx,
                None => {
                    let _ = window_clone.emit("spectrum_error", "Device index not set");
                    streaming_state_clone
                        .is_streaming
                        .store(false, Ordering::SeqCst);
                    return;
                }
            },
            Err(_) => {
                let _ = window_clone.emit("spectrum_error", "Failed to lock device index mutex");
                streaming_state_clone
                    .is_streaming
                    .store(false, Ordering::SeqCst);
                return;
            }
        };

        // Get the center frequency from mutex
        let mut current_center_freq = match streaming_state_clone.center_frequency.lock() {
            Ok(guard) => match *guard {
                Some(freq) => freq,
                None => {
                    let _ = window_clone.emit("spectrum_error", "Center frequency not set");
                    streaming_state_clone
                        .is_streaming
                        .store(false, Ordering::SeqCst);
                    return;
                }
            },
            Err(_) => {
                let _ =
                    window_clone.emit("spectrum_error", "Failed to lock center frequency mutex");
                streaming_state_clone
                    .is_streaming
                    .store(false, Ordering::SeqCst);
                return;
            }
        };

        // Open the device safely
        let mut device = match safe_open_device(device_index_i32) {
            Ok(dev) => dev,
            Err(e) => {
                let _ =
                    window_clone.emit("spectrum_error", format!("Failed to open device: {}", e));
                streaming_state_clone
                    .is_streaming
                    .store(false, Ordering::SeqCst);
                return;
            }
        };

        // — tune the hardware just once —
        if let Err(e) = device.set_sample_rate(2_048_000) {
            let _ = window_clone.emit(
                "spectrum_error",
                format!("Failed to set sample rate: {:?}", e),
            );
            streaming_state_clone
                .is_streaming
                .store(false, Ordering::SeqCst);
            return;
        }
        if let Err(e) = device.set_center_freq(current_center_freq) {
            let _ = window_clone.emit(
                "spectrum_error",
                format!("Failed to tune frequency: {:?}", e),
            );
            streaming_state_clone
                .is_streaming
                .store(false, Ordering::SeqCst);
            return;
        }
        if let Err(e) = device.reset_buffer() {
            let _ = window_clone.emit("spectrum_error", format!("Failed to reset buffer: {:?}", e));
            streaming_state_clone
                .is_streaming
                .store(false, Ordering::SeqCst);
            return;
        }

        // Configure device with better settings
        let sample_rate = 3_000_000;
        if let Err(e) = device.set_sample_rate(sample_rate) {
            let _ = window_clone.emit(
                "spectrum_error",
                format!("Failed to set sample rate: {:?}", e),
            );
            streaming_state_clone
                .is_streaming
                .store(false, Ordering::SeqCst);
            return;
        }

        if let Err(e) = device.set_center_freq(current_center_freq) {
            let _ = window_clone.emit(
                "spectrum_error",
                format!("Failed to set frequency: {:?}", e),
            );
            streaming_state_clone
                .is_streaming
                .store(false, Ordering::SeqCst);
            return;
        }

        if let Err(_e) = device.set_tuner_gain_mode(true) {
            let _ = window_clone.emit(
                "spectrum_error",
                format!("Failed to set gain mode: {:?}", _e),
            );
            // Continue anyway
        }

        if let Err(_e) = device.set_tuner_gain(400) {
            // Remove debug print, just continue
        }

        if let Err(e) = device.reset_buffer() {
            let _ = window_clone.emit("spectrum_error", format!("Failed to reset buffer: {:?}", e));
            streaming_state_clone
                .is_streaming
                .store(false, Ordering::SeqCst);
            return;
        }

        // Setup FFT
        let fft_size = 1024;
        let buffer_size = fft_size * 2; // For I/Q pairs
        let mut planner = FftPlanner::new();
        let fft = planner.plan_fft_forward(fft_size);

        // Main streaming loop
        while streaming_state_clone.is_streaming.load(Ordering::SeqCst) {
            // Check if frequency changed
            if let Ok(freq_guard) = streaming_state_clone.center_frequency.lock() {
                if let Some(new_freq) = *freq_guard {
                    if new_freq != current_center_freq {
                        if let Err(e) = device.set_center_freq(new_freq) {
                            let _ = window_clone.emit(
                                "spectrum_error",
                                format!("Failed to update frequency: {:?}", e),
                            );
                        } else {
                            current_center_freq = new_freq;
                        }
                    }
                }
            }

            // Read samples
            match device.read_sync(buffer_size) {
                Ok(buffer) => {
                    if buffer.len() >= buffer_size {
                        // Convert to complex samples
                        let mut samples = Vec::with_capacity(fft_size);
                        for i in (0..buffer_size).step_by(2) {
                            if i + 1 < buffer.len() {
                                let i_sample = (buffer[i] as f32 - 127.5) / 127.5;
                                let q_sample = (buffer[i + 1] as f32 - 127.5) / 127.5;
                                samples.push(Complex32::new(i_sample, q_sample));
                            }
                        }

                        // Pad if needed
                        while samples.len() < fft_size {
                            samples.push(Complex32::new(0.0, 0.0));
                        }

                        // Perform FFT
                        let mut fft_samples = samples.clone();

                        // Apply Hann window to reduce spectral leakage
                        for i in 0..fft_samples.len() {
                            let window = 0.5
                                * (1.0
                                    - (2.0 * std::f32::consts::PI * i as f32
                                        / fft_samples.len() as f32)
                                        .cos());
                            fft_samples[i] = fft_samples[i] * window;
                        }

                        fft.process(&mut fft_samples);

                        // Calculate power spectrum
                        let half_fft = fft_size / 2;

                        // First pass: calculate raw power levels and find min/max
                        let mut raw_powers: Vec<f32> = Vec::with_capacity(1024);
                        let mut max_power = 0.0f32;
                        let mut min_power = f32::MAX;

                        for i in 0..1024 {
                            let fft_index = i % fft_size;
                            let idx = (fft_index + half_fft) % fft_size;

                            let power = fft_samples[idx].norm();
                            raw_powers.push(power);

                            if power > max_power {
                                max_power = power;
                            }
                            if power < min_power {
                                min_power = power;
                            }
                        }

                        // Calculate noise floor (use 25th percentile as estimate)
                        let mut sorted_powers = raw_powers.clone();
                        sorted_powers
                            .sort_by(|a, b| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal));
                        let noise_floor = sorted_powers[sorted_powers.len() / 4];

                        // Second pass: apply non-linear scaling to enhance peaks
                        let mut power_levels: Vec<f32> = Vec::with_capacity(1024);
                        for power in &raw_powers {
                            // Normalize power relative to noise floor
                            let normalized = (power - noise_floor) / (max_power - noise_floor);
                            let normalized = normalized.max(0.0).min(1.0);

                            // Apply non-linear scaling to enhance peaks
                            let enhanced = normalized.powf(0.5); // Square root for more emphasis

                            // Convert to dB scale with enhancement factor applied
                            let db = 10.0 * (power.max(1e-10)).log10();

                            // Use the enhanced value to boost stronger signals
                            let boosted_db = db + (enhanced * 15.0) - 7.5; // Apply boost based on enhanced value

                            power_levels.push(boosted_db.max(-80.0).min(10.0));
                        }

                        // Get the current smoothing window size
                        let window_size = {
                            let smoothing = streaming_state_clone.smoothing_window.lock().unwrap();
                            *smoothing
                        };

                        // Apply a wider moving average for smoother display across frequency
                        let mut smoothed_power_levels: Vec<f32> = Vec::with_capacity(1024);
                        for i in 0..1024 {
                            let half_window = window_size / 2;
                            let mut sum = 0.0;
                            let mut count = 0;

                            // Convert i to usize and use explicit type for half_window
                            let i_usize = i as usize;
                            let half_window_usize = half_window;

                            // Use checked arithmetic with explicit types
                            let start_idx = if i_usize >= half_window_usize {
                                i_usize - half_window_usize
                            } else {
                                0
                            };

                            let end_idx = std::cmp::min(i_usize + half_window_usize, 1023);

                            for j in start_idx..=end_idx {
                                sum += power_levels[j];
                                count += 1;
                            }

                            smoothed_power_levels.push(sum / count as f32);
                        }

                        // Apply time-based averaging with previous spectra
                        let mut time_smoothed_levels = smoothed_power_levels.clone();
                        {
                            let mut previous_spectra =
                                streaming_state_clone.previous_spectra.lock().unwrap();

                            // If we have previous spectra, blend with current spectrum
                            if !previous_spectra.is_empty() {
                                // Calculate average of previous spectra
                                let mut avg_previous = vec![0.0f32; 1024];
                                for prev_spectrum in previous_spectra.iter() {
                                    for (i, &val) in prev_spectrum.iter().enumerate() {
                                        avg_previous[i] += val;
                                    }
                                }

                                // Divide by number of previous spectra to get average
                                let num_previous = previous_spectra.len() as f32;
                                for val in &mut avg_previous {
                                    *val /= num_previous;
                                }

                                // Blend current with previous (60% previous, 40% new)
                                for i in 0..1024 {
                                    time_smoothed_levels[i] =
                                        0.6 * avg_previous[i] + 0.4 * smoothed_power_levels[i];
                                }
                            }

                            // Add current spectrum to history
                            previous_spectra.push_back(smoothed_power_levels.clone());

                            // Keep only the last 5 spectra
                            if previous_spectra.len() > 5 {
                                previous_spectra.pop_front();
                            }
                        }

                        // Calculate frequencies
                        let bin_width = sample_rate as f32 / 1024 as f32;
                        let frequencies: Vec<f32> = (0..1024)
                            .map(|i| {
                                let offset = (i as i32 - 512) as f32 * bin_width;
                                current_center_freq as f32 + offset
                            })
                            .collect();

                        // Send update to frontend
                        let update = SpectrumUpdate {
                            frequencies,
                            power_levels: time_smoothed_levels,
                            center_frequency: current_center_freq,
                            timestamp: std::time::SystemTime::now()
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_secs(),
                        };

                        if let Err(_e) = window_clone.emit("spectrum_update", &update) {
                            // Only log critical errors
                            println!("Failed to emit spectrum_update event: {:?}", _e);
                        }
                    }
                }
                Err(e) => {
                    let _ = window_clone.emit("spectrum_error", format!("Read error: {:?}", e));
                    thread::sleep(Duration::from_millis(100));
                }
            }

            // Small delay to prevent CPU overload
            thread::sleep(Duration::from_millis(50));
        }

        println!("Spectrum streaming stopped");

        // Release the device
        release_device();
    });

    Ok("Spectrum streaming started".to_string())
}

#[tauri::command]
fn stop_spectrum_stream(streaming_state: State<'_, StreamingStateRef>) -> Result<String, String> {
    streaming_state.is_streaming.store(false, Ordering::SeqCst);

    // Release the device
    release_device();

    Ok("Spectrum streaming stopped".to_string())
}

#[tauri::command]
fn update_stream_frequency(
    center_frequency: u32,
    streaming_state: State<'_, StreamingStateRef>,
) -> Result<String, String> {
    // Send immediate command to audio thread
    if let Ok(sender_guard) = streaming_state.device_command_sender.lock() {
        if let Some(sender) = sender_guard.as_ref() {
            if sender
                .send(DeviceCommand::SetFrequency(center_frequency))
                .is_ok()
            {
                return Ok(format!("Frequency live-updated to {} Hz", center_frequency));
            }
        }
    }

    // Fallback: Update the frequency in shared state for flag-based approach
    if let Ok(mut freq) = streaming_state.center_frequency.lock() {
        *freq = Some(center_frequency);
    }
    streaming_state
        .frequency_update_needed
        .store(true, Ordering::SeqCst);

    Ok(format!("Frequency update queued: {} Hz", center_frequency))
}

#[tauri::command]
fn set_ppm(ppm: i32, streaming_state: State<'_, StreamingStateRef>) -> Result<String, String> {
    // Send immediate command to audio thread
    if let Ok(sender_guard) = streaming_state.device_command_sender.lock() {
        if let Some(sender) = sender_guard.as_ref() {
            if sender.send(DeviceCommand::SetPPM(ppm)).is_ok() {
                return Ok(format!("PPM live-updated to {} ppm", ppm));
            }
        }
    }

    // Fallback: Store PPM value in shared state for flag-based approach
    if let Ok(mut ppm_guard) = streaming_state.ppm_correction.lock() {
        *ppm_guard = Some(ppm);
    }
    streaming_state
        .ppm_update_needed
        .store(true, Ordering::SeqCst);

    Ok(format!("PPM correction queued: {} ppm", ppm))
}

#[tauri::command]
fn set_smoothing_window(
    window_size: usize,
    streaming_state: State<'_, StreamingStateRef>,
) -> Result<String, String> {
    // Ensure window size is odd (for symmetric window)
    let window_size = if window_size % 2 == 0 {
        window_size + 1
    } else {
        window_size
    };

    // Clamp to reasonable range
    let window_size = window_size.max(1).min(21);

    if let Ok(mut smoothing) = streaming_state.smoothing_window.lock() {
        *smoothing = window_size;
        Ok(format!("Smoothing window set to {}", window_size))
    } else {
        Err("Failed to set smoothing window".to_string())
    }
}

// Audio streaming commands
#[tauri::command]
fn start_audio_stream(
    device_index: usize,
    center_frequency: u32,
    demod_mode: String,
    audio_state: State<'_, AudioStateRef>,
    audio_stream_state: State<'_, AudioStreamStateRef>,
    streaming_state: State<'_, StreamingStateRef>,
) -> Result<String, String> {
    // 1) Prevent conflicts with spectrum streaming
    {
        let device_in_use = DEVICE_IN_USE.lock().unwrap();
        if device_in_use.is_some() {
            return Err("Device is currently in use by spectrum streaming. Please stop spectrum streaming first.".to_string());
        }
    }

    // 2) Parse demodulation mode
    let demod = match demod_mode.as_str() {
        "FM" => DemodulationMode::FM,
        "AM" => DemodulationMode::AM,
        "USB" => DemodulationMode::USB,
        "LSB" => DemodulationMode::LSB,
        _ => return Err("Invalid demodulation mode".to_string()),
    };

    // 3) Update shared audio state
    {
        let mut st = audio_state.lock().unwrap();
        st.is_streaming = true;
        st.demod_mode = Some(demod);
    }

    // --- Setup CPAL output + ringbuffer ---
    let host = cpal::default_host();
    let device = host
        .default_output_device()
        .ok_or("No audio output device available")?;
    let default_config = device
        .default_output_config()
        .map_err(|e| format!("Failed to get audio config: {}", e))?;
    let config = default_config.clone().into();
    let audio_sample_rate = default_config.sample_rate().0 as usize;

    // Use a larger ring buffer to prevent underruns/overruns
    // Buffer size: 0.5 seconds of audio to smooth out timing variations
    let buffer_size = audio_sample_rate / 2; // 0.5 seconds
    let ring_buffer = HeapRb::<f32>::new(buffer_size);
    let (producer, consumer) = ring_buffer.split();

    {
        let mut ss = audio_stream_state.lock().unwrap();
        ss.audio_producer = Some(producer);
        ss.is_active.store(true, Ordering::SeqCst);
    }

    let stream = match default_config.sample_format() {
        SampleFormat::F32 => create_audio_stream::<f32>(&device, &config, consumer),
        SampleFormat::I16 => create_audio_stream::<i16>(&device, &config, consumer),
        SampleFormat::U16 => create_audio_stream::<u16>(&device, &config, consumer),
        fmt => return Err(format!("Unsupported audio format: {:?}", fmt)),
    }?;
    stream
        .play()
        .map_err(|e| format!("Failed to start audio stream: {}", e))?;
    std::mem::forget(stream);

    // --- Spawn the SDR‐reader thread ---
    let device_index_i32 = device_index as i32;
    let audio_state_clone = Arc::clone(&*audio_state);
    let audio_stream_state_clone = Arc::clone(&*audio_stream_state);
    let streaming_state_clone = Arc::clone(&*streaming_state);
    let target_audio_rate = audio_sample_rate as u32;

    // Initialize streaming state with current values
    {
        if let Ok(mut freq) = streaming_state.center_frequency.lock() {
            *freq = Some(center_frequency);
        }
    }

    // Create channel for immediate device commands
    let (command_sender, command_receiver) = std::sync::mpsc::channel::<DeviceCommand>();

    // Store the sender in streaming state
    {
        if let Ok(mut sender_guard) = streaming_state.device_command_sender.lock() {
            *sender_guard = Some(command_sender);
        }
    }

    let _handle = thread::spawn(move || {
        // a) open & lock the SDR device
        let mut dev = match safe_open_device(device_index_i32) {
            Ok(d) => d,
            Err(e) => {
                eprintln!("Audio thread: cannot open SDR: {}", e);
                return;
            }
        };

        // b) one‐time tuning
        if let Err(e) = dev.set_sample_rate(2_048_000) {
            eprintln!("Audio thread: set_sample_rate failed: {:?}", e);
            release_device();
            return;
        }
        if let Err(e) = dev.set_center_freq(center_frequency) {
            eprintln!("Audio thread: set_center_freq failed: {:?}", e);
            release_device();
            return;
        }
        if let Err(e) = dev.reset_buffer() {
            eprintln!("Audio thread: reset_buffer failed: {:?}", e);
            release_device();
            return;
        }

        // c) rtl_fm–style gain + de‐emphasis
        let _ = dev.set_agc_mode(false);
        let _ = dev.set_tuner_gain_mode(true);
        let _ = dev.set_tuner_gain(496);
        let _ = dev.set_tuner_bandwidth(143_650);

        // d) main loop: read, demod, filter, downsample, push
        const SDR_RATE: usize = 2_048_000;
        let decimation_factor = SDR_RATE / (target_audio_rate as usize).max(1);

        // Calculate buffer size to produce consistent audio chunks
        // Target: ~10ms of audio per processing cycle for smooth playback
        let target_audio_chunk_ms = 10; // 10ms chunks
        let target_audio_samples_per_chunk =
            (target_audio_rate as usize * target_audio_chunk_ms) / 1000;
        let sdr_samples_needed = target_audio_samples_per_chunk * decimation_factor;
        let buf_size = sdr_samples_needed * 2; // I/Q pairs
        let mut _current_frequency = center_frequency;
        let mut current_ppm: Option<i32> = None;

        // Timing control for consistent audio production
        let target_loop_duration = std::time::Duration::from_millis(target_audio_chunk_ms as u64);
        let mut last_loop_time = std::time::Instant::now();

        while audio_stream_state_clone
            .lock()
            .unwrap()
            .is_active
            .load(Ordering::SeqCst)
        {
            // Check for immediate device commands (non-blocking)
            while let Ok(command) = command_receiver.try_recv() {
                match command {
                    DeviceCommand::SetFrequency(freq) => {
                        if let Err(e) = dev.set_center_freq(freq) {
                            eprintln!("Audio thread: Failed to set frequency via command: {:?}", e);
                        } else {
                            // CRITICAL: Flush the device buffer to clear old I/Q samples
                            if let Err(e) = dev.reset_buffer() {
                                eprintln!("Audio thread: Failed to flush buffer after frequency change: {:?}", e);
                            }
                            // Give the hardware a moment to settle on the new LO
                            std::thread::sleep(std::time::Duration::from_millis(100));

                            // Drain out any old audio from the ring buffer
                            if let Ok(mut ss) = audio_stream_state_clone.lock() {
                                if let Some(prod) = &mut ss.audio_producer {
                                    while prod.push(0.0).is_ok() {}
                                }
                            }

                            println!(
                                "Audio thread: Live-updated frequency to {} Hz (buffer flushed, settled, audio drained)",
                                freq
                            );
                            _current_frequency = freq;
                        }
                    }
                    DeviceCommand::SetPPM(ppm) => {
                        if let Err(e) = dev.set_freq_correction(ppm) {
                            eprintln!("Audio thread: Failed to set PPM via command: {:?}", e);
                        } else {
                            // CRITICAL: Flush the device buffer to clear old I/Q samples
                            if let Err(e) = dev.reset_buffer() {
                                eprintln!(
                                    "Audio thread: Failed to flush buffer after PPM change: {:?}",
                                    e
                                );
                            }
                            // Give the hardware a moment to settle on the new PPM
                            std::thread::sleep(std::time::Duration::from_millis(100));

                            // Drain out any old audio from the ring buffer
                            if let Ok(mut ss) = audio_stream_state_clone.lock() {
                                if let Some(prod) = &mut ss.audio_producer {
                                    while prod.push(0.0).is_ok() {}
                                }
                            }

                            println!("Audio thread: Live-updated PPM to {} (buffer flushed, settled, audio drained)", ppm);
                            current_ppm = Some(ppm);
                        }
                    }
                }
            }

            // Check for immediate frequency update flag (fallback)
            if streaming_state_clone
                .frequency_update_needed
                .load(Ordering::SeqCst)
            {
                if let Ok(freq_guard) = streaming_state_clone.center_frequency.lock() {
                    if let Some(new_freq) = *freq_guard {
                        if let Err(e) = dev.set_center_freq(new_freq) {
                            eprintln!("Audio thread: Failed to update frequency: {:?}", e);
                        } else {
                            // CRITICAL: Flush the device buffer to clear old I/Q samples
                            if let Err(e) = dev.reset_buffer() {
                                eprintln!("Audio thread: Failed to flush buffer after frequency change: {:?}", e);
                            }
                            // Give the hardware a moment to settle on the new LO
                            std::thread::sleep(std::time::Duration::from_millis(100));

                            // Drain out any old audio from the ring buffer
                            if let Ok(mut ss) = audio_stream_state_clone.lock() {
                                if let Some(prod) = &mut ss.audio_producer {
                                    while prod.push(0.0).is_ok() {}
                                }
                            }

                            println!(
                                "Audio thread: Updated frequency to {} Hz (buffer flushed, settled, audio drained)",
                                new_freq
                            );
                            _current_frequency = new_freq;
                            // Clear the flag after successful update
                            streaming_state_clone
                                .frequency_update_needed
                                .store(false, Ordering::SeqCst);
                        }
                    }
                }
            }

            // Check for immediate PPM update flag (fallback)
            if streaming_state_clone
                .ppm_update_needed
                .load(Ordering::SeqCst)
            {
                if let Ok(ppm_guard) = streaming_state_clone.ppm_correction.lock() {
                    if let Some(new_ppm) = *ppm_guard {
                        // Only update if it's different from current PPM
                        if Some(new_ppm) != current_ppm {
                            if let Err(e) = dev.set_freq_correction(new_ppm) {
                                eprintln!("Audio thread: Failed to update PPM: {:?}", e);
                            } else {
                                // CRITICAL: Flush the device buffer to clear old I/Q samples
                                if let Err(e) = dev.reset_buffer() {
                                    eprintln!("Audio thread: Failed to flush buffer after PPM change: {:?}", e);
                                }
                                // Give the hardware a moment to settle on the new PPM
                                std::thread::sleep(std::time::Duration::from_millis(100));

                                // Drain out any old audio from the ring buffer
                                if let Ok(mut ss) = audio_stream_state_clone.lock() {
                                    if let Some(prod) = &mut ss.audio_producer {
                                        while prod.push(0.0).is_ok() {}
                                    }
                                }

                                println!(
                                    "Audio thread: Updated PPM correction to {} (buffer flushed, settled, audio drained)",
                                    new_ppm
                                );
                                current_ppm = Some(new_ppm);
                            }
                        }
                        // Clear the flag regardless of success/failure to prevent spam
                        streaming_state_clone
                            .ppm_update_needed
                            .store(false, Ordering::SeqCst);
                    }
                }
            }
            // Use read_async with timeout to allow more frequent polling
            match dev.read_sync(buf_size) {
                Ok(buf) if buf.len() >= buf_size => {
                    // Convert to I/Q with proper DC centering
                    let mut iq = Vec::with_capacity(buf_size / 2);
                    for chunk in buf.chunks_exact(2) {
                        // Convert unsigned 8-bit to signed, then normalize to [-1, 1]
                        // RTL-SDR outputs 0-255, center is 127.5
                        let i = (chunk[0] as f32 - 127.5) / 127.5;
                        let q = (chunk[1] as f32 - 127.5) / 127.5;
                        iq.push(Complex32::new(i, q));
                    }

                    // FM demod (at SDR rate)
                    let squelch_threshold = audio_state_clone.lock().unwrap().squelch_threshold;
                    let mut audio = demodulate_fm(&iq, SDR_RATE as f32, squelch_threshold);

                    // Apply anti-aliasing filter BEFORE resampling
                    apply_antialias_filter(&mut audio, 15000.0 / (SDR_RATE as f32 / 2.0));

                    // Fixed-size decimation for consistent timing
                    let volume = audio_state_clone.lock().unwrap().volume;

                    // Use the pre-calculated decimation factor for consistency
                    let expected_output_samples = target_audio_samples_per_chunk;
                    let mut chunk = Vec::with_capacity(expected_output_samples);

                    // Use precise decimation to maintain consistent sample rate
                    for i in 0..expected_output_samples {
                        let src_idx = i * decimation_factor;
                        if src_idx < audio.len() {
                            chunk.push(audio[src_idx] * volume);
                        } else {
                            // Pad with silence if we don't have enough samples
                            chunk.push(0.0);
                        }
                    }

                    // Apply de-emphasis and filtering AFTER decimation (at correct sample rate)
                    apply_fm_audio_filter(&mut chunk);

                    // 2) AGC-normalize to some target RMS (e.g. 0.1)
                    agc_normalize(&mut chunk, 0.1);

                    // 3) clamp & push into the ring buffer with buffer level monitoring
                    let mut pushed = 0;
                    if let Ok(mut ss) = audio_stream_state_clone.lock() {
                        if let Some(prod) = &mut ss.audio_producer {
                            // Check buffer level before pushing
                            let buffer_space = prod.free_len();
                            let buffer_used = prod.len();

                            // If buffer is getting too full, skip some samples to prevent overflow
                            let skip_samples = if buffer_space < chunk.len() {
                                chunk.len() - buffer_space
                            } else {
                                0
                            };

                            for (i, &s) in chunk.iter().enumerate() {
                                if i < skip_samples {
                                    continue; // Skip samples if buffer is too full
                                }

                                let s = s.clamp(-1.0, 1.0);
                                if prod.push(s).is_ok() {
                                    pushed += 1;
                                } else {
                                    break; // Buffer full
                                }
                            }

                            // Log buffer status occasionally
                            static mut BUFFER_STATUS_COUNTER: u32 = 0;
                            unsafe {
                                BUFFER_STATUS_COUNTER += 1;
                                if BUFFER_STATUS_COUNTER % 100 == 0 {
                                    println!(
                                        "Buffer status: {} used, {} free, {} skipped",
                                        buffer_used, buffer_space, skip_samples
                                    );
                                }
                            }
                        }
                    }
                    println!("Pushed {} normalized samples", pushed);

                    // Timing control: ensure we don't produce audio too fast
                    let elapsed = last_loop_time.elapsed();
                    if elapsed < target_loop_duration {
                        let sleep_time = target_loop_duration - elapsed;
                        std::thread::sleep(sleep_time);
                    }
                    last_loop_time = std::time::Instant::now();
                }
                Ok(_) => { /* skip small buffers */ }
                Err(e) => {
                    eprintln!("Audio thread read error: {:?}", e);
                    thread::sleep(Duration::from_millis(50));
                }
            }
        }

        // e) cleanup
        println!("Audio streaming stopped");
        release_device();
    });

    Ok("Audio streaming started".into())
}

#[tauri::command]
fn stop_audio_stream(
    audio_state: State<'_, AudioStateRef>,
    audio_stream_state: State<'_, AudioStreamStateRef>,
    streaming_state: State<'_, StreamingStateRef>,
) -> Result<String, String> {
    // Update audio state
    {
        let mut state = audio_state.lock().unwrap();
        state.is_streaming = false;
    }

    // Stop audio stream
    {
        let mut stream_state = audio_stream_state.lock().unwrap();
        stream_state.is_active.store(false, Ordering::SeqCst);
        stream_state.audio_producer = None;
    }

    // Clear the command sender
    {
        if let Ok(mut sender_guard) = streaming_state.device_command_sender.lock() {
            *sender_guard = None;
        }
    }

    // Release the device
    release_device();

    Ok("Audio streaming stopped".to_string())
}

#[tauri::command]
fn set_audio_volume(volume: f32, audio_state: State<'_, AudioStateRef>) -> Result<String, String> {
    let volume = volume.max(0.0).min(1.0); // Clamp between 0 and 1

    let mut state = audio_state.lock().unwrap();
    state.volume = volume;

    Ok(format!("Volume set to {:.0}%", volume * 100.0))
}

#[tauri::command]
fn set_audio_mute(muted: bool, audio_state: State<'_, AudioStateRef>) -> Result<String, String> {
    let mut state = audio_state.lock().unwrap();
    state.muted = muted;

    Ok(if muted {
        "Audio muted".to_string()
    } else {
        "Audio unmuted".to_string()
    })
}

#[tauri::command]
fn get_audio_state(audio_state: State<'_, AudioStateRef>) -> Result<serde_json::Value, String> {
    let state = audio_state.lock().unwrap();

    Ok(serde_json::json!({
        "is_streaming": state.is_streaming,
        "volume": state.volume,
        "muted": state.muted,
        "demod_mode": state.demod_mode,
        "sample_rate": state.sample_rate,
        "squelch_threshold": state.squelch_threshold
    }))
}

#[tauri::command]
fn set_squelch_threshold(
    threshold: f32,
    audio_state: State<'_, AudioStateRef>,
) -> Result<String, String> {
    let threshold = threshold.max(0.0).min(1.0); // Clamp between 0 and 1

    let mut state = audio_state.lock().unwrap();
    state.squelch_threshold = threshold;

    Ok(format!("Squelch threshold set to {:.3}", threshold))
}

// Helper function to create audio stream
fn create_audio_stream<T>(
    device: &Device,
    config: &StreamConfig,
    mut consumer: HeapConsumer<f32>,
) -> Result<Stream, String>
where
    T: cpal::Sample + cpal::SizedSample + cpal::FromSample<f32>,
{
    let channels = config.channels as usize;
    println!("Creating audio stream with {} channels", channels);

    let stream = device
        .build_output_stream(
            config,
            move |data: &mut [T], _: &cpal::OutputCallbackInfo| {
                let mut samples_consumed = 0;
                let mut underruns = 0;

                for frame in data.chunks_mut(channels) {
                    let sample = if let Some(s) = consumer.pop() {
                        if s != 0.0 {
                            samples_consumed += 1;
                        }
                        s
                    } else {
                        // Buffer underrun - use silence to maintain timing
                        underruns += 1;
                        0.0
                    };

                    for channel in frame.iter_mut() {
                        *channel = T::from_sample(sample);
                    }
                }

                // Only log occasionally to avoid spam
                static mut LOG_COUNTER: u32 = 0;
                unsafe {
                    LOG_COUNTER += 1;
                    if LOG_COUNTER % 100 == 0 {
                        println!(
                            "Audio callback: {} samples, {} underruns, buffer available: {}",
                            samples_consumed,
                            underruns,
                            consumer.len()
                        );
                    }
                }
            },
            |err| eprintln!("Audio stream error: {}", err),
            None,
        )
        .map_err(|e| format!("Failed to create audio stream: {}", e))?;

    Ok(stream)
}

// Test audio output with a simple tone
#[tauri::command]
fn test_audio_output() -> Result<String, String> {
    use std::f32::consts::PI;

    println!("Starting audio test...");

    // Initialize audio output
    let host = cpal::default_host();
    let device = host
        .default_output_device()
        .ok_or("No audio output device available")?;

    let config = device
        .default_output_config()
        .map_err(|e| format!("Failed to get audio config: {}", e))?;

    println!("Audio config: {:?}", config);

    // Create a simple sine wave generator
    let sample_rate = config.sample_rate().0 as f32;
    let frequency = 440.0; // A4 note
    let mut phase = 0.0f32;
    let phase_increment = 2.0 * PI * frequency / sample_rate;

    let stream = match config.sample_format() {
        cpal::SampleFormat::F32 => {
            device.build_output_stream(
                &config.into(),
                move |data: &mut [f32], _: &cpal::OutputCallbackInfo| {
                    for sample in data.iter_mut() {
                        *sample = (phase.sin() * 0.3) as f32; // 30% volume
                        phase += phase_increment;
                        if phase >= 2.0 * PI {
                            phase -= 2.0 * PI;
                        }
                    }
                },
                |err| eprintln!("Audio stream error: {}", err),
                None,
            )
        }
        cpal::SampleFormat::I16 => device.build_output_stream(
            &config.into(),
            move |data: &mut [i16], _: &cpal::OutputCallbackInfo| {
                for sample in data.iter_mut() {
                    let value = (phase.sin() * 0.3 * i16::MAX as f32) as i16;
                    *sample = value;
                    phase += phase_increment;
                    if phase >= 2.0 * PI {
                        phase -= 2.0 * PI;
                    }
                }
            },
            |err| eprintln!("Audio stream error: {}", err),
            None,
        ),
        cpal::SampleFormat::U16 => device.build_output_stream(
            &config.into(),
            move |data: &mut [u16], _: &cpal::OutputCallbackInfo| {
                for sample in data.iter_mut() {
                    let value = ((phase.sin() * 0.3 + 1.0) * 0.5 * u16::MAX as f32) as u16;
                    *sample = value;
                    phase += phase_increment;
                    if phase >= 2.0 * PI {
                        phase -= 2.0 * PI;
                    }
                }
            },
            |err| eprintln!("Audio stream error: {}", err),
            None,
        ),
        _ => return Err("Unsupported audio format".to_string()),
    }
    .map_err(|e| format!("Failed to create test audio stream: {}", e))?;

    stream
        .play()
        .map_err(|e| format!("Failed to start test audio stream: {}", e))?;

    println!("Playing test tone for 2 seconds...");

    // Play for 2 seconds
    std::thread::sleep(std::time::Duration::from_secs(2));

    // Stream will be dropped here, stopping the audio
    println!("Test tone finished");

    Ok("Audio test completed - you should have heard a 440Hz tone for 2 seconds".to_string())
}

// Test the audio pipeline with the same system used for SDR audio
#[tauri::command]
fn test_audio_pipeline(
    _audio_state: State<'_, AudioStateRef>,
    audio_stream_state: State<'_, AudioStreamStateRef>,
) -> Result<String, String> {
    use std::f32::consts::PI;

    println!("Testing audio pipeline...");

    // Initialize audio output with the same system as SDR audio
    let host = cpal::default_host();
    let device = host
        .default_output_device()
        .ok_or("No audio output device available")?;

    let default_config = device
        .default_output_config()
        .map_err(|e| format!("Failed to get audio config: {}", e))?;

    // Use the device's native configuration
    let config = default_config.clone().into();
    let audio_sample_rate = default_config.sample_rate().0 as usize;

    // Create ring buffer for audio samples
    let ring_buffer = HeapRb::<f32>::new(audio_sample_rate * 2);
    let (producer, consumer) = ring_buffer.split();

    println!(
        "Pipeline test: {} Hz, {} channels, format: {:?}",
        audio_sample_rate,
        default_config.channels(),
        default_config.sample_format()
    );

    // Store producer in audio stream state
    {
        let mut stream_state = audio_stream_state.lock().unwrap();
        stream_state.audio_producer = Some(producer);
        stream_state
            .is_active
            .store(true, std::sync::atomic::Ordering::SeqCst);
    }

    // Create audio output stream using the device's native format
    let stream = match default_config.sample_format() {
        cpal::SampleFormat::F32 => create_audio_stream::<f32>(&device, &config, consumer),
        cpal::SampleFormat::I16 => create_audio_stream::<i16>(&device, &config, consumer),
        cpal::SampleFormat::U16 => create_audio_stream::<u16>(&device, &config, consumer),
        _ => return Err("Unsupported audio format".to_string()),
    }?;
    println!("Audio stream created successfully");

    stream
        .play()
        .map_err(|e| format!("Failed to start test audio stream: {}", e))?;
    println!("Audio stream started playing");

    // Give the stream a moment to initialize
    std::thread::sleep(std::time::Duration::from_millis(100));

    // Generate test audio samples and push them through the pipeline
    let frequency = 440.0; // A4 note
    let mut phase = 0.0f32;
    let phase_increment = 2.0 * PI * frequency / audio_sample_rate as f32;

    // Push samples in chunks to simulate real-time audio
    let chunk_size = 1024; // Process in chunks like real SDR
    let total_samples = audio_sample_rate * 2; // 2 seconds of audio
    let mut samples_pushed = 0;

    for chunk_start in (0..total_samples).step_by(chunk_size) {
        let chunk_end = (chunk_start + chunk_size).min(total_samples);

        // Generate a chunk of samples
        let mut chunk_samples_pushed = 0;
        for _ in chunk_start..chunk_end {
            let sample = (phase.sin() * 0.3) as f32; // 30% volume

            if let Ok(mut stream_state) = audio_stream_state.lock() {
                if let Some(ref mut prod) = stream_state.audio_producer {
                    if prod.push(sample).is_ok() {
                        samples_pushed += 1;
                        chunk_samples_pushed += 1;
                    }
                }
            }

            phase += phase_increment;
            if phase >= 2.0 * PI {
                phase -= 2.0 * PI;
            }
        }

        if chunk_samples_pushed > 0 {
            println!(
                "Pushed {} samples in chunk {}",
                chunk_samples_pushed,
                chunk_start / chunk_size
            );
        }

        // Small delay to simulate real-time processing
        std::thread::sleep(std::time::Duration::from_millis(20));
    }

    println!("Pipeline test: pushed {} samples", samples_pushed);

    // Wait for audio to play and keep the stream alive
    println!("Waiting for audio to play...");
    for i in 0..20 {
        // Wait 2 seconds in 100ms chunks
        std::thread::sleep(std::time::Duration::from_millis(100));
        if i % 10 == 0 {
            println!("Still waiting... ({}/2 seconds)", i / 10);
        }
    }

    // Clean up
    {
        let mut stream_state = audio_stream_state.lock().unwrap();
        stream_state
            .is_active
            .store(false, std::sync::atomic::Ordering::SeqCst);
        stream_state.audio_producer = None;
    }

    // Keep stream alive until the end
    drop(stream);
    println!("Audio stream stopped");

    Ok("Audio pipeline test completed - you should have heard a 440Hz tone".to_string())
}

// Test FM demodulation with a synthetic FM signal
#[tauri::command]
fn test_fm_with_tone(
    _audio_state: State<'_, AudioStateRef>,
    audio_stream_state: State<'_, AudioStreamStateRef>,
) -> Result<String, String> {
    use std::f32::consts::PI;

    println!("Testing FM demodulation with synthetic signal…");

    // 1) setup audio output & ring buffer
    let host = cpal::default_host();
    let device = host
        .default_output_device()
        .ok_or("No audio output device available")?;
    let default_cfg = device
        .default_output_config()
        .map_err(|e| format!("Failed to get audio config: {}", e))?;
    let cfg: StreamConfig = default_cfg.clone().into();
    let audio_rate = default_cfg.sample_rate().0 as usize;
    let ring = HeapRb::<f32>::new(audio_rate * 2);
    let (producer, consumer) = ring.split();
    {
        let mut st = audio_stream_state.lock().unwrap();
        st.audio_producer = Some(producer);
        st.is_active.store(true, Ordering::SeqCst);
    }
    let stream = create_audio_stream::<f32>(&device, &cfg, consumer).map_err(|e| e)?;
    stream.play().map_err(|e| format!("{}", e))?;
    println!("FM test stream started");

    // 2) generate synthetic FM I/Q
    let sdr_rate = 2_048_000.0;
    let total = (sdr_rate * 2.0) as usize; // 2 s
    let mut iq = Vec::with_capacity(total);
    let carrier = 100.0;
    let mod_freq = 1_000.0;
    let depth = 5.0;
    for n in 0..total {
        let t = n as f32 / sdr_rate;
        let mod_sig = (2.0 * PI * mod_freq * t).sin();
        let phase = 2.0 * PI * carrier * t + depth * mod_sig;
        iq.push(Complex32::new(phase.cos(), phase.sin()));
    }

    // 3) FM demodulate + de-emphasis
    let mut audio = demodulate_fm(&iq, sdr_rate, 0.01); // Use default squelch for test
    apply_fm_audio_filter(&mut audio);

    // 4) down-sample & push
    const SDR_RATE: usize = 2_048_000;
    let ratio = SDR_RATE / audio_rate;
    let mut pushed = 0;
    for (i, &s) in audio.iter().enumerate() {
        if i % ratio != 0 {
            continue;
        }
        let s = s.clamp(-1.0, 1.0);
        if let Ok(mut st) = audio_stream_state.lock() {
            if let Some(prod) = &mut st.audio_producer {
                if prod.push(s).is_ok() {
                    pushed += 1;
                }
            }
        }
    }
    println!("Pushed {} FM audio samples to buffer", pushed);
    println!("FM test: pushed {} samples to audio buffer", pushed);

    // 5) play for 2 s then clean up
    std::thread::sleep(std::time::Duration::from_secs(2));
    {
        let mut st = audio_stream_state.lock().unwrap();
        st.is_active.store(false, Ordering::SeqCst);
        st.audio_producer = None;
    }
    drop(stream);

    Ok("FM test completed — check the console for raw demodulated samples.".into())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let signal_data = Arc::new(Mutex::new(SignalData::new()));
    let streaming_state = Arc::new(StreamingState::new());
    let audio_state = Arc::new(Mutex::new(AudioState::new()));
    let audio_stream_state = Arc::new(Mutex::new(AudioStreamState::new()));

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(signal_data)
        .manage(streaming_state)
        .manage(audio_state)
        .manage(audio_stream_state)
        .invoke_handler(tauri::generate_handler![
            greet,
            list_sdr_devices,
            tune_sdr,
            start_signal_monitor,
            stop_signal_monitor,
            get_signal_data,
            get_spectrum,
            start_spectrum_stream,
            stop_spectrum_stream,
            update_stream_frequency,
            set_ppm,
            set_smoothing_window,
            start_audio_stream,
            stop_audio_stream,
            set_audio_volume,
            set_audio_mute,
            get_audio_state,
            set_squelch_threshold,
            test_audio_output,
            test_audio_pipeline,
            test_fm_with_tone
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
