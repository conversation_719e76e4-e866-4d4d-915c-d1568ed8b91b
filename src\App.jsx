import { useState, useEffect, useRef } from "react";
import Chart from 'chart.js/auto';
import SpectrumAnalyzer from './SpectrumAnalyzer';
import AudioControls from './AudioControls';
// Fix the imports for Tauri API v2
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

function App() {
  const [devices, setDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [frequency, setFrequency] = useState(100.1);
  const [tuneStatus, setTuneStatus] = useState("");
  const [error, setError] = useState("");
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [signalData, setSignalData] = useState([]);
  const [isSpectrumActive, setIsSpectrumActive] = useState(false);
  const [spectrumDebug, setSpectrumDebug] = useState(null);
  const [tauriLoaded, setTauriLoaded] = useState(false);
  const [spectrumData, setSpectrumData] = useState(null);
  const [continuousSpectrum, setContinuousSpectrum] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isAudioStreaming, setIsAudioStreaming] = useState(false);
  const [ppm, setPpm] = useState(0);
  const [smoothingWindow, setSmoothingWindow] = useState(11);
  const spectrumIntervalRef = useRef(null);
  
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  // Check if Tauri is available
  useEffect(() => {
    // Import Tauri API dynamically
    const loadTauriAPI = async () => {
      try {
        // Try to import the Tauri API
        const tauriAPI = await import('@tauri-apps/api');
        setTauriLoaded(true);
        
        // Store the API in window for easier access
        window.__TAURI_API__ = tauriAPI;
      } catch (err) {
        console.error("Failed to import Tauri API:", err);
      }
    };
    
    loadTauriAPI();
  }, []);

  // Function to safely invoke Tauri commands
  const invokeCommand = async (command, args) => {
    try {
      // For Tauri v2, we need to import the invoke function from the core module
      if (window.__TAURI_API__?.core) {
        const { invoke } = window.__TAURI_API__.core;
        console.log("Using invoke from window.__TAURI_API__.core:", invoke);
        return await invoke(command, args);
      }
      
      // Fallback to global __TAURI__ object (for Tauri v1)
      if (window.__TAURI__?.invoke) {
        return await window.__TAURI__.invoke(command, args);
      }
      
      throw new Error("Tauri API not available");
    } catch (err) {
      console.error(`Error invoking ${command}:`, err);
      throw err;
    }
  };

  // Initialize chart when chartRef is available and signalData changes
  useEffect(() => {
    if (chartRef.current && signalData.length > 0) {
      // Destroy existing chart if it exists
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
      
      // Create new chart
      const ctx = chartRef.current.getContext('2d');
      chartInstance.current = new Chart(ctx, {
        type: 'line',
        data: {
          labels: signalData.map((_, i) => i),
          datasets: [{
            label: 'Signal Strength',
            data: signalData,
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    }
  }, [chartRef, signalData]);

  useEffect(() => {
    if (continuousSpectrum && selectedDevice !== null) {
      // Start continuous spectrum monitoring
      spectrumIntervalRef.current = setInterval(async () => {
        try {
          const freqHz = Math.round(frequency * 1000000);
          console.log(`Fetching spectrum data at ${freqHz} Hz`);
          
          const data = await invokeCommand('get_spectrum', {
            deviceIndex: selectedDevice,
            centerFrequency: freqHz
          });
          
          // Add timestamp to verify we're getting fresh data
          const dataWithTimestamp = {
            ...data,
            timestamp: new Date().toISOString()
          };
          
          // Force a new object reference to trigger re-renders
          setSpectrumData(dataWithTimestamp);
          
          // Update debug info
          setSpectrumDebug(JSON.stringify(data, null, 2));
        } catch (e) {
          console.error("Error in continuous spectrum monitoring:", e);
        }
      }, 500); // Update every 500ms (twice per second) instead of every 1000ms (once per second)
      
      return () => {
        if (spectrumIntervalRef.current) {
          clearInterval(spectrumIntervalRef.current);
          spectrumIntervalRef.current = null;
        }
      };
    } else if (spectrumIntervalRef.current) {
      clearInterval(spectrumIntervalRef.current);
      spectrumIntervalRef.current = null;
    }
  }, [continuousSpectrum, selectedDevice, frequency]);

  useEffect(() => {
    console.log("Setting up event listeners");
    
    // Use the Tauri API directly for event listening
    const setupListeners = async () => {
      try {
        // Listen for spectrum updates
        const unlistenSpectrum = await listen('spectrum_update', (event) => {
          console.log("RECEIVED SPECTRUM UPDATE:", event);
          
          if (!event.payload) {
            console.error("Event payload is empty");
            return;
          }
          
          // Create a new object to ensure React detects the change
          const newData = {
            ...event.payload,
            clientTimestamp: new Date().toISOString()
          };
          
          console.log("Setting spectrum data with:", {
            timestamp: newData.timestamp,
            dataPoints: newData.power_levels?.length || 0
          });
          
          setSpectrumData(newData);
        });
        
        // Listen for spectrum errors
        const unlistenError = await listen('spectrum_error', (event) => {
          console.error("Spectrum error:", event.payload);
          setError(`Spectrum error: ${event.payload}`);
        });
        
        console.log("Event listeners successfully set up");
        
        // Return cleanup function
        return () => {
          console.log("Cleaning up event listeners");
          unlistenSpectrum();
          unlistenError();
        };
      } catch (err) {
        console.error("Failed to set up event listeners:", err);
      }
    };
    
    // Call the async function
    const cleanupFn = setupListeners();
    
    // Return a cleanup function
    return () => {
      // Use the cleanup function returned by setupListeners
      cleanupFn.then(cleanup => {
        if (cleanup) cleanup();
      });
    };
  }, []); // Empty dependency array means this runs once on mount

  useEffect(() => {
    if ((isStreaming || isAudioStreaming) && selectedDevice !== null) {
      // Update frequency when it changes during streaming (spectrum or audio)
      const updateFrequency = async () => {
        try {
          const freqHz = Math.round(frequency * 1000000);
          await invokeCommand('update_stream_frequency', {
            centerFrequency: freqHz
          });
        } catch (e) {
          console.error("Error updating frequency:", e);
        }
      };

      updateFrequency();
    }
  }, [frequency, isStreaming, isAudioStreaming, selectedDevice]);

  // Add a function to toggle streaming
  const toggleStreaming = async () => {
    try {
      if (isStreaming) {
        console.log("Stopping spectrum stream");
        await invokeCommand('stop_spectrum_stream');
        setIsStreaming(false);
      } else {
        console.log("Starting spectrum stream");
        const freqHz = Math.round(frequency * 1000000);
        await invokeCommand('start_spectrum_stream', {
          deviceIndex: selectedDevice,
          centerFrequency: freqHz
        });
        setIsStreaming(true);
        
        // Update status text with PPM when starting streaming
        setTuneStatus(`Device ${selectedDevice} tuned to ${frequency} MHz${ppm !== 0 ? ` (${ppm} ppm)` : ''}`);
      }
    } catch (e) {
      console.error("Error toggling spectrum stream:", e);
      setError(`Failed to toggle spectrum stream: ${e}`);
    }
  };

  // Add this useEffect to load devices automatically when Tauri is ready
  useEffect(() => {
    // Load devices when Tauri API is available
    const loadDevices = async () => {
      if (tauriLoaded) {
        try {
          console.log("Auto-loading devices...");
          const deviceList = await invokeCommand('list_sdr_devices');
          console.log("Devices loaded:", deviceList);
          setDevices(deviceList);
        } catch (e) {
          console.error("Error auto-loading devices:", e);
          setError(`Failed to load devices: ${e}`);
        }
      }
    };
    
    loadDevices();
  }, [tauriLoaded]); // Run when tauriLoaded changes to true

  const handleSmoothingChange = (newValue) => {
    setSmoothingWindow(parseInt(newValue));
    // Send the new value to the backend
    if (selectedDevice !== null) {
      invokeCommand('set_smoothing_window', { windowSize: parseInt(newValue) })
        .catch(e => {
          setError(`Failed to set smoothing: ${e}`);
        });
    }
  };

  // Add this effect to update the frequency display when PPM changes
  useEffect(() => {
    if (selectedDevice !== null) {
      setTuneStatus(`Device ${selectedDevice} tuned to ${frequency} MHz${ppm !== 0 ? ` (${ppm} ppm)` : ''}`);
    }
  }, [ppm, selectedDevice, frequency]);

  return (
    <div className="container" style={{
      padding: "0.5rem",
      margin: "0",
      height: "100vh",
      display: "flex",
      flexDirection: "column",
      overflowY: "auto",
      overflowX: "hidden",
      boxSizing: "border-box"
    }}>
      <h1 style={{ margin: "0.25rem 0", textAlign: "center", fontSize: "1.5rem" }}>SDR Watch</h1>
      
      {/* Device Selection */}
      <div className="card" style={{ 
        padding: "0.25rem", 
        margin: "0 0.5rem",
        border: "1px solid #ddd",
        borderRadius: "4px"
      }}>
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
          <label htmlFor="device-select" style={{ fontSize: "0.9rem" }}>Choose device:</label>
          <select 
            id="device-select"
            value={selectedDevice !== null ? selectedDevice : ""} 
            onChange={(e) => setSelectedDevice(e.target.value !== "" ? parseInt(e.target.value) : null)}
            style={{ padding: "0.25rem", flex: "1" }}
          >
            <option value="">Select a device</option>
            {devices.map((device, index) => (
              <option key={index} value={index}>{device}</option>
            ))}
          </select>
          <button 
            onClick={async () => {
              try {
                if (!tauriLoaded) throw new Error("Tauri API not loaded yet");
                const deviceList = await invokeCommand('list_sdr_devices');
                setDevices(deviceList);
                setError("");
              } catch (e) {
                setError(`Failed to list devices: ${e}`);
              }
            }}
            style={{ padding: "0.25rem 0.5rem", fontSize: "0.9rem" }}
          >
            Refresh
          </button>
        </div>
      </div>
      
      {/* Frequency Tuning */}
      <div className="card" style={{
        padding: "0.5rem",
        margin: "0.25rem 0.5rem",
        border: "1px solid #ddd",
        borderRadius: "4px"
      }}>
        {/* Frequency Display */}
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem", marginBottom: "0.5rem" }}>
          <label style={{ fontSize: "0.9rem", fontWeight: "bold" }}>Frequency: {frequency.toFixed(1)} MHz</label>
          <span style={{
            flex: "1",
            color: isStreaming ? "blue" : "green",
            fontSize: "1rem",
            textAlign: "center",
            fontWeight: "500"
          }}>
            {selectedDevice !== null
              ? (tuneStatus || `Device ${selectedDevice} tuned to ${frequency} MHz`)
              : "Select a device to tune"}
          </span>
        </div>

        {/* FM Band Slider (88-108 MHz) */}
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem", marginBottom: "0.5rem" }}>
          <label style={{ fontSize: "0.8rem", minWidth: "60px" }}>FM Band:</label>
          <input
            type="range"
            min="88"
            max="108"
            step="0.1"
            value={frequency >= 88 && frequency <= 108 ? frequency : 100.7}
            onChange={(e) => {
              const newFreq = parseFloat(e.target.value);
              setFrequency(newFreq);

              // Real-time frequency update for both streaming and non-streaming
              if (selectedDevice !== null) {
                setTuneStatus(`Device ${selectedDevice} tuned to ${newFreq} MHz${ppm !== 0 ? ` (${ppm} ppm)` : ''}`);

                const freqHz = Math.round(newFreq * 1000000);
                if (isStreaming || isAudioStreaming) {
                  // Update streaming frequency in real-time
                  invokeCommand('update_stream_frequency', {
                    centerFrequency: freqHz
                  }).catch(e => {
                    setError(`Failed to update frequency: ${e}`);
                  });
                } else {
                  // Update non-streaming frequency in real-time
                  invokeCommand('tune_sdr', {
                    deviceIndex: selectedDevice,
                    frequency: freqHz
                  }).catch(e => {
                    setError(`Failed to tune: ${e}`);
                  });
                }
              }
            }}
            style={{ flex: "1", height: "20px" }}
          />
          <span style={{ fontSize: "0.8rem", minWidth: "40px" }}>{frequency >= 88 && frequency <= 108 ? frequency.toFixed(1) : "88-108"}</span>
        </div>

        {/* Preset Buttons */}
        <div style={{ display: "flex", gap: "0.25rem", marginBottom: "0.5rem" }}>
          {[100.7, 101.5, 102.3, 103.1, 104.9, 106.7].map(freq => (
            <button
              key={freq}
              onClick={() => {
                setFrequency(freq);
                if (selectedDevice !== null) {
                  setTuneStatus(`Device ${selectedDevice} tuned to ${freq} MHz${ppm !== 0 ? ` (${ppm} ppm)` : ''}`);

                  const freqHz = Math.round(freq * 1000000);
                  if (isStreaming || isAudioStreaming) {
                    invokeCommand('update_stream_frequency', {
                      centerFrequency: freqHz
                    }).catch(e => {
                      setError(`Failed to update frequency: ${e}`);
                    });
                  } else {
                    invokeCommand('tune_sdr', {
                      deviceIndex: selectedDevice,
                      frequency: freqHz
                    }).catch(e => {
                      setError(`Failed to tune: ${e}`);
                    });
                  }
                }
              }}
              style={{
                padding: "0.2rem 0.4rem",
                fontSize: "0.7rem",
                backgroundColor: frequency === freq ? "#007bff" : "#f8f9fa",
                color: frequency === freq ? "white" : "black",
                border: "1px solid #ddd",
                borderRadius: "3px",
                cursor: "pointer"
              }}
            >
              {freq}
            </button>
          ))}
        </div>

        {/* Manual Input */}
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
          <label htmlFor="frequency-input" style={{ fontSize: "0.8rem", minWidth: "60px" }}>Manual:</label>
          <input
            id="frequency-input"
            type="number"
            value={frequency}
            onChange={(e) => {
              const newFreq = parseFloat(e.target.value);
              if (!isNaN(newFreq)) {
                setFrequency(newFreq);

                // Update status immediately
                if (selectedDevice !== null) {
                  setTuneStatus(`Device ${selectedDevice} tuned to ${newFreq} MHz${ppm !== 0 ? ` (${ppm} ppm)` : ''}`);
                }
              }
            }}
            onBlur={(e) => {
              // Apply frequency change when user finishes editing
              if (selectedDevice !== null) {
                const freqHz = Math.round(frequency * 1000000);
                if (isStreaming || isAudioStreaming) {
                  invokeCommand('update_stream_frequency', {
                    centerFrequency: freqHz
                  }).catch(e => {
                    setError(`Failed to update frequency: ${e}`);
                  });
                } else {
                  invokeCommand('tune_sdr', {
                    deviceIndex: selectedDevice,
                    frequency: freqHz
                  }).catch(e => {
                    setError(`Failed to tune: ${e}`);
                  });
                }
              }
            }}
            step="0.1"
            min="24"
            max="1766"
            style={{
              width: "80px",
              padding: "0.25rem"
            }}
          />
          <span>MHz</span>
        </div>
      </div>

      {/* PPM Correction */}
      <div className="card" style={{
        padding: "0.5rem",
        margin: "0.25rem 0.5rem",
        border: "1px solid #ddd",
        borderRadius: "4px"
      }}>
        {/* PPM Display */}
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem", marginBottom: "0.5rem" }}>
          <label style={{ fontSize: "0.9rem", fontWeight: "bold" }}>PPM Correction: {ppm}</label>
          <span style={{
            flex: "1",
            fontSize: "0.8rem",
            fontStyle: "italic",
            color: "#666",
            textAlign: "right"
          }}>
            Real-time frequency drift correction
          </span>
        </div>

        {/* PPM Slider */}
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem", marginBottom: "0.5rem" }}>
          <label style={{ fontSize: "0.8rem", minWidth: "60px" }}>Fine Tune:</label>
          <input
            type="range"
            min="-50"
            max="50"
            step="1"
            value={ppm}
            onChange={(e) => {
              const newPpm = parseInt(e.target.value);
              setPpm(newPpm);

              // Real-time PPM update
              if (selectedDevice !== null) {
                setTuneStatus(`Device ${selectedDevice} tuned to ${frequency} MHz (${newPpm} ppm)`);

                invokeCommand('set_ppm', {
                  deviceIndex: selectedDevice,
                  ppm: newPpm
                })
                .then(() => {
                  setError("");

                  // Re-tune to apply the correction immediately
                  const freqHz = Math.round(frequency * 1000000);
                  if (isStreaming || isAudioStreaming) {
                    return invokeCommand('update_stream_frequency', {
                      centerFrequency: freqHz
                    });
                  } else {
                    return invokeCommand('tune_sdr', {
                      deviceIndex: selectedDevice,
                      frequency: freqHz
                    });
                  }
                })
                .catch(e => {
                  setError(`Failed to set PPM: ${e}`);
                });
              }
            }}
            style={{ flex: "1", height: "20px" }}
          />
          <span style={{ fontSize: "0.8rem", minWidth: "40px" }}>{ppm}</span>
        </div>

        {/* PPM Preset Buttons */}
        <div style={{ display: "flex", gap: "0.25rem", marginBottom: "0.5rem" }}>
          {[-20, -10, -5, 0, 5, 10, 20].map(ppmValue => (
            <button
              key={ppmValue}
              onClick={() => {
                setPpm(ppmValue);
                if (selectedDevice !== null) {
                  setTuneStatus(`Device ${selectedDevice} tuned to ${frequency} MHz (${ppmValue} ppm)`);

                  invokeCommand('set_ppm', {
                    deviceIndex: selectedDevice,
                    ppm: ppmValue
                  })
                  .then(() => {
                    setError("");

                    const freqHz = Math.round(frequency * 1000000);
                    if (isStreaming || isAudioStreaming) {
                      return invokeCommand('update_stream_frequency', {
                        centerFrequency: freqHz
                      });
                    } else {
                      return invokeCommand('tune_sdr', {
                        deviceIndex: selectedDevice,
                        frequency: freqHz
                      });
                    }
                  })
                  .catch(e => {
                    setError(`Failed to set PPM: ${e}`);
                  });
                }
              }}
              style={{
                padding: "0.2rem 0.4rem",
                fontSize: "0.7rem",
                backgroundColor: ppm === ppmValue ? "#007bff" : "#f8f9fa",
                color: ppm === ppmValue ? "white" : "black",
                border: "1px solid #ddd",
                borderRadius: "3px",
                cursor: "pointer"
              }}
            >
              {ppmValue > 0 ? `+${ppmValue}` : ppmValue}
            </button>
          ))}
        </div>

        {/* Manual PPM Input */}
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
          <label htmlFor="ppm-input" style={{ fontSize: "0.8rem", minWidth: "60px" }}>Manual:</label>
          <input
            id="ppm-input"
            type="number"
            value={ppm}
            onChange={(e) => {
              const newPpm = parseInt(e.target.value);
              if (!isNaN(newPpm)) {
                setPpm(newPpm);
              }
            }}
            onBlur={() => {
              // Apply PPM correction when focus leaves the input
              if (selectedDevice !== null) {
                setTuneStatus(`Device ${selectedDevice} tuned to ${frequency} MHz (${ppm} ppm)`);

                invokeCommand('set_ppm', {
                  deviceIndex: selectedDevice,
                  ppm: ppm
                })
                .then(() => {
                  setError("");

                  const freqHz = Math.round(frequency * 1000000);
                  if (isStreaming || isAudioStreaming) {
                    return invokeCommand('update_stream_frequency', {
                      centerFrequency: freqHz
                    });
                  } else {
                    return invokeCommand('tune_sdr', {
                      deviceIndex: selectedDevice,
                      frequency: freqHz
                    });
                  }
                })
                .catch(e => {
                  setError(`Failed to set PPM: ${e}`);
                });
              }
            }}
            step="1"
            min="-100"
            max="100"
            style={{
              width: "70px",
              padding: "0.25rem"
            }}
          />
          <span>ppm</span>
        </div>
      </div>

      {/* Spectrum Settings */}
      <div className="card" style={{ 
        padding: "0.25rem", 
        margin: "0.25rem 0.5rem",
        border: "1px solid #ddd",
        borderRadius: "4px"
      }}>
        <div style={{ display: "flex", alignItems: "center", gap: "0.5rem" }}>
          <label htmlFor="smoothing-slider" style={{ fontSize: "0.9rem" }}>Frequency smoothing:</label>
          <input
            id="smoothing-slider"
            type="range"
            min="1"
            max="21"
            step="2" // Only odd numbers for symmetric window
            value={smoothingWindow}
            onChange={(e) => handleSmoothingChange(e.target.value)}
            style={{ flex: "1" }}
          />
          <span style={{ width: "30px", textAlign: "right" }}>{smoothingWindow}</span>
          <span style={{ 
            fontSize: "0.9rem",
            fontStyle: "italic",
            color: "#666",
            marginLeft: "0.5rem"
          }}>
            {smoothingWindow <= 5 ? "More detail" : 
             smoothingWindow >= 15 ? "More smooth" : "Balanced"}
          </span>
        </div>
      </div>

      {/* Audio Controls */}
      <AudioControls
        selectedDevice={selectedDevice}
        frequency={frequency}
        isSpectrumStreaming={isStreaming}
        onAudioStreamingChange={setIsAudioStreaming}
      />

      {/* Spectrum Analysis */}
      <div style={{
        padding: "0.25rem",
        margin: "0.25rem 0.5rem",
        border: "1px solid #ddd",
        borderRadius: "4px",
        height: "300px",
        display: "flex",
        flexDirection: "column",
        flexShrink: 0
      }}>
        <div style={{ display: "flex", justifyContent: "center", marginBottom: "0.25rem" }}>
          <button
            onClick={toggleStreaming}
            disabled={selectedDevice === null}
            style={{
              backgroundColor: isStreaming ? '#dc3545' : (selectedDevice === null ? '#6c757d' : '#28a745'),
              color: 'white',
              border: 'none',
              padding: '0.25rem 0.75rem',
              borderRadius: '4px',
              cursor: selectedDevice === null ? 'not-allowed' : 'pointer',
              opacity: selectedDevice === null ? 0.65 : 1
            }}
          >
            {isStreaming ? 'Stop Streaming' : 'Start Streaming'}
          </button>
        </div>
        
        <div style={{
          height: "250px",
          overflow: "hidden"
        }}>
          <SpectrumAnalyzer 
            key={spectrumData ? spectrumData.timestamp : 'empty'} 
            spectrumData={spectrumData || {
              frequencies: Array(1024).fill(0).map((_, i) => i * 1000000),
              power_levels: Array(1024).fill(-80),
              center_frequency: frequency * 1000000,
              timestamp: Date.now()
            }} 
          />
        </div>
      </div>
      
      {error && (
        <div style={{ color: "red", padding: "0.25rem", textAlign: "center" }}>
          {error}
        </div>
      )}
    </div>
  );
}

export default App;
